#!/usr/bin/env python3
"""
简单调试脚本 - 保存页面HTML并查找元素
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def simple_debug():
    """简单调试"""
    
    options = Options()
    options.add_argument('--window-size=1920,1080')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    try:
        url = "https://www.nvidia.cn/geforce/drivers/"
        logging.info(f"访问页面: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 保存初始页面HTML
        with open('nvidia_initial.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        logging.info("已保存初始页面HTML到 nvidia_initial.html")
        
        # 处理cookies
        try:
            accept_btn = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '全部接受')]"))
            )
            accept_btn.click()
            logging.info("已处理cookies")
            time.sleep(2)
        except:
            logging.info("未发现cookies弹窗")
        
        # 查找所有包含"搜索"的元素
        search_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '搜索')]")
        logging.info(f"找到 {len(search_elements)} 个包含'搜索'的元素")
        
        for i, elem in enumerate(search_elements):
            try:
                logging.info(f"搜索元素 {i+1}: {elem.tag_name} - {elem.get_attribute('class')} - {elem.text}")
                logging.info(f"  可点击: {elem.is_enabled()}")
                logging.info(f"  ID: {elem.get_attribute('id')}")
            except:
                pass
        
        # 尝试点击第一个可点击的搜索元素
        for elem in search_elements:
            try:
                if elem.is_enabled() and elem.is_displayed():
                    logging.info(f"尝试点击: {elem.text}")
                    elem.click()
                    break
            except Exception as e:
                logging.info(f"点击失败: {e}")
                continue
        
        # 等待结果加载
        logging.info("等待15秒让页面加载...")
        time.sleep(15)
        
        # 保存点击后的页面HTML
        with open('nvidia_after_search.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        logging.info("已保存搜索后页面HTML到 nvidia_after_search.html")
        
        # 查找驱动相关元素
        driver_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'GeForce') or contains(text(), 'Driver')]")
        logging.info(f"找到 {len(driver_elements)} 个驱动相关元素")
        
        for i, elem in enumerate(driver_elements[:5]):
            try:
                logging.info(f"驱动元素 {i+1}: {elem.tag_name}.{elem.get_attribute('class')} - {elem.text[:50]}")
            except:
                pass
        
        logging.info("调试完成，请查看保存的HTML文件")
        
    except Exception as e:
        logging.error(f"调试失败: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    simple_debug()
