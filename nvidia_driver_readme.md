# NVIDIA驱动监控程序

自动监控 https://www.nvidia.cn/geforce/drivers/ 并下载最新驱动程序。

## 功能特点

- 🍪 自动处理cookies弹窗（点击"全部接受"）
- 🔍 自动访问NVIDIA驱动页面并点击"开始搜索"
- 📦 自动识别最新的GeForce Game Ready驱动
- ⬇️ 自动下载新版本驱动到`drivers`文件夹
- 📝 记录已下载版本，避免重复下载
- 🔄 支持一次性检查或持续监控模式
- 📊 详细的日志记录
- 🛡️ 多重备用选择器，提高稳定性

## 安装依赖

```bash
pip install selenium webdriver-manager
```

## 使用方法

### 方式1: 使用启动脚本（推荐）
```bash
start_nvidia_monitor.bat
```
然后按提示选择版本和运行模式。

### 方式2: 直接运行Python脚本

#### 简洁版（推荐）
```bash
# 一次性检查
python nvidia_driver_monitor_simple.py --once

# 持续监控模式（默认6小时检查一次）
python nvidia_driver_monitor_simple.py
```

#### 健壮版（更多错误处理）
```bash
# 一次性检查
python nvidia_driver_monitor_robust.py --once

# 持续监控模式
python nvidia_driver_monitor_robust.py
```

## 文件说明

- `nvidia_driver_monitor_simple.py` - 简洁版监控程序（推荐）
- `nvidia_driver_monitor_robust.py` - 健壮版监控程序（更多错误处理）
- `nvidia_driver_monitor.py` - 完整版监控程序
- `start_nvidia_monitor.bat` - Windows启动脚本
- `drivers/` - 驱动下载目录（自动创建）
- `nvidia_versions.json` - 已下载版本记录
- `nvidia_monitor.log` - 日志文件

## 工作原理

1. 使用Selenium自动化浏览器访问NVIDIA驱动页面
2. 自动处理cookies弹窗（点击"全部接受"）
3. 使用精确的XPath定位并点击"开始搜索"按钮
4. 等待驱动列表加载完成（增加了足够的等待时间）
5. 解析页面中的驱动信息（名称、版本号、发布日期）
6. 对比本地记录，识别新版本驱动
7. 自动点击下载按钮启动下载
8. 记录已下载版本，避免重复下载

## 注意事项

- 首次运行会自动下载Chrome驱动
- 需要Chrome浏览器环境
- 下载的驱动文件保存在`drivers`文件夹
- 程序会自动处理页面加载和JavaScript渲染
- 支持Windows环境下的.exe驱动文件下载

## 自定义配置

可以修改程序中的以下参数：
- `hours=6` - 监控间隔时间（小时）
- `download_dir` - 下载目录路径
- `data_file` - 版本记录文件名

## 故障排除

如果遇到问题：
1. 确保Chrome浏览器已安装
2. 检查网络连接
3. 查看日志文件了解详细错误信息
4. 尝试手动访问NVIDIA网站确认页面结构未变化
