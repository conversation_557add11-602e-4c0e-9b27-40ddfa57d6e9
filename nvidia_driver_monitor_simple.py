#!/usr/bin/env python3
"""
NVIDIA驱动更新监控程序 - 简洁版
监控 https://www.nvidia.cn/geforce/drivers/ 并自动下载新驱动
"""

import os
import time
import json
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

class NvidiaDriverMonitor:
    def __init__(self):
        self.url = "https://www.nvidia.cn/geforce/drivers/"
        self.download_dir = "drivers"
        self.data_file = "nvidia_versions.json"
        self.driver = None
        
        os.makedirs(self.download_dir, exist_ok=True)
        self.downloaded_versions = self.load_versions()
    
    def load_versions(self):
        """加载已下载版本记录"""
        try:
            with open(self.data_file, 'r') as f:
                return set(json.load(f))
        except:
            return set()
    
    def save_versions(self):
        """保存版本记录"""
        with open(self.data_file, 'w') as f:
            json.dump(list(self.downloaded_versions), f)
    
    def setup_driver(self):
        """设置Chrome驱动"""
        if self.driver:
            return self.driver
            
        options = Options()
        options.add_argument('--window-size=1920,1080')
        
        # 设置下载目录
        prefs = {
            "download.default_directory": os.path.abspath(self.download_dir),
            "download.prompt_for_download": False
        }
        options.add_experimental_option("prefs", prefs)
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=options)
        return self.driver
    
    def handle_cookies(self):
        """处理cookies弹窗"""
        try:
            # 等待并处理cookies弹窗
            accept_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '全部接受') or contains(text(), '接受') or contains(text(), 'Accept')]"))
            )
            accept_btn.click()
            logging.info("已接受cookies")
            time.sleep(2)
        except:
            logging.info("未发现cookies弹窗或已处理")

    def get_latest_drivers(self):
        """获取最新驱动信息"""
        driver = self.setup_driver()
        drivers = []

        try:
            logging.info("访问NVIDIA驱动页面...")
            driver.get(self.url)

            # 处理cookies弹窗
            self.handle_cookies()

            # 使用你提供的精确XPath点击搜索按钮
            search_btn = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div/div[2]/div/div/div/div/div[2]/div[2]/div/div[4]/div[1]/form/div[2]/div[8]/a/div"))
            )
            search_btn.click()
            logging.info("已点击搜索按钮")

            # 等待更长时间让驱动列表加载
            logging.info("等待驱动列表加载...")
            time.sleep(10)  # 增加等待时间
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'GeForce Game Ready')]"))
            )
            
            # 提取驱动信息
            driver_cards = driver.find_elements(By.XPATH, "//div[contains(text(), 'GeForce Game Ready')]/..")
            
            for card in driver_cards:
                try:
                    # 获取驱动名称
                    name = card.find_element(By.XPATH, ".//div[contains(text(), 'GeForce Game Ready')]").text
                    
                    # 获取版本和日期信息
                    version_info = card.find_element(By.XPATH, ".//div[contains(text(), '驱动程序版本')]").text
                    
                    # 解析版本号
                    version = ""
                    if "驱动程序版本:" in version_info:
                        version = version_info.split("驱动程序版本:")[1].split("-")[0].strip()
                    
                    # 获取下载按钮
                    download_btn = card.find_element(By.XPATH, ".//button[contains(text(), '获取下载')]")
                    
                    if version:
                        drivers.append({
                            'name': name,
                            'version': version,
                            'download_btn': download_btn
                        })
                        logging.info(f"发现驱动: {name} - 版本: {version}")
                
                except Exception as e:
                    logging.warning(f"解析驱动信息失败: {e}")
                    continue
            
            return drivers
            
        except Exception as e:
            logging.error(f"获取驱动信息失败: {e}")
            return []
    
    def download_new_drivers(self, drivers):
        """下载新驱动"""
        new_downloads = 0
        
        for driver_info in drivers:
            version = driver_info['version']
            
            if version in self.downloaded_versions:
                logging.info(f"版本 {version} 已下载，跳过")
                continue
            
            try:
                logging.info(f"开始下载: {driver_info['name']} - {version}")
                
                # 点击下载按钮
                driver_info['download_btn'].click()
                
                # 等待下载页面加载
                time.sleep(10)
                
                # 尝试点击最终下载链接
                try:
                    final_btn = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//a[contains(@href, '.exe')] | //button[contains(text(), '下载')]"))
                    )
                    final_btn.click()
                    time.sleep(5)
                except:
                    logging.info("可能已开始下载或无需额外点击")
                
                # 记录已下载版本
                self.downloaded_versions.add(version)
                self.save_versions()
                new_downloads += 1
                
                logging.info(f"驱动 {version} 下载已启动")
                
                # 返回主页面继续下载其他驱动
                if len(drivers) > 1:
                    self.driver.back()
                    time.sleep(3)
                
            except Exception as e:
                logging.error(f"下载驱动 {version} 失败: {e}")
        
        return new_downloads
    
    def check_updates(self):
        """检查并下载更新"""
        try:
            drivers = self.get_latest_drivers()
            
            if not drivers:
                logging.warning("未找到任何驱动")
                return
            
            new_count = self.download_new_drivers(drivers)
            
            if new_count > 0:
                logging.info(f"成功下载 {new_count} 个新驱动")
            else:
                logging.info("没有新的驱动更新")
                
        except Exception as e:
            logging.error(f"检查更新失败: {e}")
        finally:
            if self.driver:
                self.driver.quit()
                self.driver = None
    
    def run_once(self):
        """运行一次检查"""
        logging.info("开始检查NVIDIA驱动更新...")
        self.check_updates()
        logging.info("检查完成")
    
    def run_monitor(self, hours=6):
        """持续监控模式"""
        logging.info(f"开始监控模式，每 {hours} 小时检查一次")
        
        while True:
            try:
                self.check_updates()
                logging.info(f"等待 {hours} 小时后进行下次检查...")
                time.sleep(hours * 3600)
            except KeyboardInterrupt:
                logging.info("监控已停止")
                break
            except Exception as e:
                logging.error(f"监控出错: {e}")
                time.sleep(300)  # 出错后等待5分钟

def main():
    monitor = NvidiaDriverMonitor()
    
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        monitor.run_once()
    else:
        monitor.run_monitor(hours=6)

if __name__ == "__main__":
    main()
