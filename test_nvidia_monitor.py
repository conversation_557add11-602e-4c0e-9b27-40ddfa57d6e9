#!/usr/bin/env python3
"""
NVIDIA驱动监控程序测试脚本
用于验证程序是否能正常访问页面和处理元素
"""

import sys
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def test_nvidia_page():
    """测试NVIDIA页面访问和元素定位"""
    
    # 设置Chrome驱动
    options = Options()
    options.add_argument('--window-size=1920,1080')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    try:
        url = "https://www.nvidia.cn/geforce/drivers/"
        logging.info(f"访问页面: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 测试cookies处理
        logging.info("检查cookies弹窗...")
        try:
            cookie_selectors = [
                "//button[contains(text(), '全部接受')]",
                "//button[contains(text(), '接受')]",
                "//button[contains(text(), 'Accept')]"
            ]
            
            for selector in cookie_selectors:
                try:
                    accept_btn = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    accept_btn.click()
                    logging.info(f"✓ 成功处理cookies (选择器: {selector})")
                    time.sleep(2)
                    break
                except:
                    continue
            else:
                logging.info("✓ 未发现cookies弹窗或已处理")
        except Exception as e:
            logging.info(f"cookies处理: {e}")
        
        # 测试搜索按钮定位
        logging.info("测试搜索按钮定位...")
        search_selectors = [
            "/html/body/div[1]/div/div[2]/div/div/div/div/div[2]/div[2]/div/div[4]/div[1]/form/div[2]/div[8]/a/div",
            "//a[contains(@class, 'btn') and contains(., '开始搜索')]",
            "//button[contains(text(), '开始搜索')]",
            "//a[contains(text(), '开始搜索')]",
            "//*[contains(text(), '开始搜索')]"
        ]
        
        search_btn = None
        for selector in search_selectors:
            try:
                search_btn = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                logging.info(f"✓ 找到搜索按钮 (选择器: {selector})")
                break
            except:
                logging.warning(f"✗ 选择器失败: {selector}")
                continue
        
        if not search_btn:
            logging.error("✗ 未找到搜索按钮")
            return False
        
        # 点击搜索按钮
        logging.info("点击搜索按钮...")
        search_btn.click()
        
        # 等待驱动列表加载
        logging.info("等待驱动列表加载...")
        time.sleep(10)
        
        # 测试驱动列表定位
        driver_selectors = [
            "//div[contains(text(), 'GeForce Game Ready')]",
            "//div[contains(text(), 'Game Ready')]",
            "//*[contains(text(), '驱动程序版本')]"
        ]
        
        drivers_found = False
        for selector in driver_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    logging.info(f"✓ 找到 {len(elements)} 个驱动元素 (选择器: {selector})")
                    drivers_found = True
                    
                    # 显示前几个驱动信息
                    for i, elem in enumerate(elements[:3]):
                        try:
                            text = elem.text.strip()
                            if text:
                                logging.info(f"  驱动 {i+1}: {text}")
                        except:
                            pass
                    break
            except:
                logging.warning(f"✗ 驱动选择器失败: {selector}")
                continue
        
        if not drivers_found:
            logging.error("✗ 未找到驱动列表")
            return False
        
        # 测试下载按钮定位
        logging.info("测试下载按钮定位...")
        download_selectors = [
            "//button[contains(text(), '获取下载')]",
            "//a[contains(text(), '获取下载')]",
            "//button[contains(text(), '下载')]"
        ]
        
        download_btns_found = 0
        for selector in download_selectors:
            try:
                buttons = driver.find_elements(By.XPATH, selector)
                if buttons:
                    download_btns_found += len(buttons)
                    logging.info(f"✓ 找到 {len(buttons)} 个下载按钮 (选择器: {selector})")
            except:
                logging.warning(f"✗ 下载按钮选择器失败: {selector}")
        
        if download_btns_found == 0:
            logging.error("✗ 未找到下载按钮")
            return False
        
        logging.info("✓ 所有测试通过！")
        return True
        
    except Exception as e:
        logging.error(f"测试失败: {e}")
        return False
    finally:
        driver.quit()

def main():
    logging.info("开始测试NVIDIA驱动监控程序...")
    
    if test_nvidia_page():
        logging.info("🎉 测试成功！程序应该能正常工作。")
        print("\n" + "="*50)
        print("测试结果: ✓ 通过")
        print("建议使用: nvidia_driver_monitor_simple.py 或 nvidia_driver_monitor_robust.py")
        print("="*50)
    else:
        logging.error("❌ 测试失败！请检查网络连接或页面结构是否发生变化。")
        print("\n" + "="*50)
        print("测试结果: ✗ 失败")
        print("请检查:")
        print("1. 网络连接是否正常")
        print("2. Chrome浏览器是否已安装")
        print("3. NVIDIA网站是否可以正常访问")
        print("="*50)

if __name__ == "__main__":
    main()
