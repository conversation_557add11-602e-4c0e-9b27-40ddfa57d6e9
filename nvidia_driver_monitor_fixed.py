#!/usr/bin/env python3
"""
NVIDIA驱动更新监控程序 - 修复版
专门修复搜索按钮定位问题
"""

import os
import time
import json
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

class NvidiaDriverMonitor:
    def __init__(self):
        self.url = "https://www.nvidia.cn/geforce/drivers/"
        self.download_dir = "drivers"
        self.data_file = "nvidia_versions.json"
        self.driver = None
        
        os.makedirs(self.download_dir, exist_ok=True)
        self.downloaded_versions = self.load_versions()
    
    def load_versions(self):
        """加载已下载版本记录"""
        try:
            with open(self.data_file, 'r') as f:
                return set(json.load(f))
        except:
            return set()
    
    def save_versions(self):
        """保存版本记录"""
        with open(self.data_file, 'w') as f:
            json.dump(list(self.downloaded_versions), f)
    
    def setup_driver(self):
        """设置Chrome驱动"""
        if self.driver:
            return self.driver
            
        options = Options()
        options.add_argument('--window-size=1920,1080')
        
        # 设置下载目录
        prefs = {
            "download.default_directory": os.path.abspath(self.download_dir),
            "download.prompt_for_download": False
        }
        options.add_experimental_option("prefs", prefs)
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=options)
        return self.driver
    
    def handle_cookies(self):
        """处理cookies弹窗"""
        try:
            accept_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '全部接受')]"))
            )
            accept_btn.click()
            logging.info("已接受cookies")
            time.sleep(2)
        except:
            logging.info("未发现cookies弹窗")

    def find_search_button(self):
        """精确查找搜索按钮"""
        # 首先查找所有可能的搜索相关元素
        search_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '开始搜索') or contains(text(), '搜索')]")
        
        logging.info(f"找到 {len(search_elements)} 个包含搜索文本的元素")
        
        for i, elem in enumerate(search_elements):
            try:
                tag = elem.tag_name
                classes = elem.get_attribute('class')
                text = elem.text.strip()
                parent_tag = elem.find_element(By.XPATH, "..").tag_name
                
                logging.info(f"搜索元素 {i+1}: {tag}.{classes} - '{text}' (父元素: {parent_tag})")
                
                # 如果是div且包含"开始搜索"，尝试点击其父元素
                if tag == 'div' and '开始搜索' in text:
                    parent = elem.find_element(By.XPATH, "..")
                    if parent.tag_name == 'a':
                        logging.info(f"找到搜索按钮的父链接: {parent.get_attribute('class')}")
                        return parent
                
                # 如果是span且包含"开始搜索"，尝试点击其祖父元素
                elif tag == 'span' and '开始搜索' in text:
                    grandparent = elem.find_element(By.XPATH, "../..")
                    if grandparent.tag_name == 'a':
                        logging.info(f"找到搜索按钮的祖父链接: {grandparent.get_attribute('class')}")
                        return grandparent
                        
            except Exception as e:
                logging.debug(f"分析搜索元素失败: {e}")
                continue
        
        # 如果上面的方法都失败，尝试直接通过ID查找
        try:
            search_div = self.driver.find_element(By.ID, "lclStartSearchBtn")
            parent = search_div.find_element(By.XPATH, "..")
            if parent.tag_name == 'a':
                logging.info("通过ID找到搜索按钮")
                return parent
        except:
            pass
        
        logging.error("未找到搜索按钮")
        return None

    def get_latest_drivers(self):
        """获取最新驱动信息"""
        driver = self.setup_driver()
        drivers = []
        
        try:
            logging.info("访问NVIDIA驱动页面...")
            driver.get(self.url)

            # 处理cookies弹窗
            self.handle_cookies()

            # 等待搜索按钮出现
            logging.info("等待搜索按钮加载...")
            time.sleep(5)

            # 精确查找并点击搜索按钮
            search_btn = self.find_search_button()
            if not search_btn:
                return []

            # 确保元素可见并可交互
            try:
                # 滚动到元素位置
                self.driver.execute_script("arguments[0].scrollIntoView(true);", search_btn)
                time.sleep(1)

                # 等待元素可点击
                WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable(search_btn)
                )

                # 尝试点击
                search_btn.click()
                logging.info("已点击搜索按钮")
            except Exception as e:
                logging.warning(f"普通点击失败: {e}")
                # 尝试JavaScript点击
                try:
                    self.driver.execute_script("arguments[0].click();", search_btn)
                    logging.info("已通过JavaScript点击搜索按钮")
                except Exception as e2:
                    logging.error(f"JavaScript点击也失败: {e2}")
                    return []
            
            # 等待JavaScript执行和驱动列表加载
            logging.info("等待驱动列表加载...")
            time.sleep(15)
            
            # 等待驱动搜索结果容器并包含内容
            try:
                WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.ID, "betaSearchResults"))
                )
                WebDriverWait(driver, 20).until(
                    lambda driver: len(driver.find_elements(By.CSS_SELECTOR, "#betaSearchResults .driverBox")) > 0
                )
                logging.info("驱动列表已加载")
            except:
                logging.warning("等待驱动列表超时，尝试继续...")
            
            # 提取驱动信息
            driver_boxes = driver.find_elements(By.CSS_SELECTOR, ".driverBox")
            logging.info(f"找到 {len(driver_boxes)} 个驱动盒子")
            
            for box in driver_boxes:
                try:
                    # 获取驱动名称
                    name_selectors = ["a.drvrTitle", ".drvrTitle"]
                    name = ""
                    for selector in name_selectors:
                        try:
                            name_elem = box.find_element(By.CSS_SELECTOR, selector)
                            name = name_elem.text.strip()
                            if name:
                                break
                        except:
                            continue
                    
                    # 获取版本信息
                    version_selectors = [".versnDate", ".longdesc"]
                    version_text = ""
                    for selector in version_selectors:
                        try:
                            desc_elem = box.find_element(By.CSS_SELECTOR, selector)
                            version_text = desc_elem.text.strip()
                            if version_text:
                                break
                        except:
                            continue
                    
                    # 解析版本号
                    version = ""
                    if version_text:
                        if "Version:" in version_text:
                            version = version_text.split("Version:")[1].split("-")[0].strip()
                        elif "驱动程序版本:" in version_text:
                            lines = version_text.split('\n')
                            for i, line in enumerate(lines):
                                if "驱动程序版本:" in line and i + 1 < len(lines):
                                    version = lines[i + 1].strip()
                                    break
                    
                    # 获取下载按钮
                    download_selectors = ["a.link-btn", ".link-btn", ".drvDwn a"]
                    download_btn = None
                    for selector in download_selectors:
                        try:
                            download_btn = box.find_element(By.CSS_SELECTOR, selector)
                            if download_btn:
                                break
                        except:
                            continue
                    
                    if name and download_btn:
                        drivers.append({
                            'name': name,
                            'version': version if version else "未知版本",
                            'download_btn': download_btn
                        })
                        logging.info(f"发现驱动: {name} - 版本: {version}")
                
                except Exception as e:
                    logging.warning(f"解析驱动信息失败: {e}")
                    continue
            
            return drivers
            
        except Exception as e:
            logging.error(f"获取驱动信息失败: {e}")
            return []
    
    def download_new_drivers(self, drivers):
        """下载新驱动"""
        new_downloads = 0
        
        for driver_info in drivers:
            version = driver_info['version']
            
            if version in self.downloaded_versions:
                logging.info(f"版本 {version} 已下载，跳过")
                continue
            
            try:
                logging.info(f"开始下载: {driver_info['name']} - {version}")
                
                driver_info['download_btn'].click()
                time.sleep(10)
                
                try:
                    final_btn = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//a[contains(@href, '.exe')] | //button[contains(text(), '下载')]"))
                    )
                    final_btn.click()
                    time.sleep(5)
                except:
                    logging.info("可能已开始下载或无需额外点击")
                
                self.downloaded_versions.add(version)
                self.save_versions()
                new_downloads += 1
                
                logging.info(f"驱动 {version} 下载已启动")
                
                if len(drivers) > 1:
                    self.driver.back()
                    time.sleep(3)
                
            except Exception as e:
                logging.error(f"下载驱动 {version} 失败: {e}")
        
        return new_downloads
    
    def check_updates(self):
        """检查并下载更新"""
        try:
            drivers = self.get_latest_drivers()
            
            if not drivers:
                logging.warning("未找到任何驱动")
                return
            
            new_count = self.download_new_drivers(drivers)
            
            if new_count > 0:
                logging.info(f"成功下载 {new_count} 个新驱动")
            else:
                logging.info("没有新的驱动更新")
                
        except Exception as e:
            logging.error(f"检查更新失败: {e}")
        finally:
            if self.driver:
                self.driver.quit()
                self.driver = None
    
    def run_once(self):
        """运行一次检查"""
        logging.info("开始检查NVIDIA驱动更新...")
        self.check_updates()
        logging.info("检查完成")

def main():
    monitor = NvidiaDriverMonitor()
    monitor.run_once()

if __name__ == "__main__":
    main()
