#!/usr/bin/env python3
"""
NVIDIA驱动更新监控程序
监控 https://www.nvidia.cn/geforce/drivers/ 并自动下载新驱动
"""

import os
import time
import json
import logging
import requests
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager

# 配置日志
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/nvidia_driver.log'),
        logging.StreamHandler()
    ]
)

class NvidiaDriverMonitor:
    def __init__(self):
        self.url = "https://www.nvidia.cn/geforce/drivers/"
        self.download_dir = "drivers"
        self.data_file = "nvidia_drivers.json"
        self.driver = None
        
        # 创建下载目录
        os.makedirs(self.download_dir, exist_ok=True)
        
        # 加载已下载的驱动记录
        self.downloaded_drivers = self.load_downloaded_drivers()
    
    def load_downloaded_drivers(self):
        """加载已下载的驱动记录"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_downloaded_drivers(self):
        """保存已下载的驱动记录"""
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump(self.downloaded_drivers, f, indent=2, ensure_ascii=False)
    
    def get_driver(self):
        """获取Chrome WebDriver"""
        if self.driver is None:
            try:
                chrome_options = Options()
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--window-size=1920,1080')
                chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
                
                # 设置下载目录
                prefs = {
                    "download.default_directory": os.path.abspath(self.download_dir),
                    "download.prompt_for_download": False,
                    "download.directory_upgrade": True,
                    "safebrowsing.enabled": True
                }
                chrome_options.add_experimental_option("prefs", prefs)
                
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logging.info("Chrome WebDriver初始化成功")
            except Exception as e:
                logging.error(f"WebDriver初始化失败: {e}")
                raise
        return self.driver
    
    def close_driver(self):
        """关闭WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
                logging.info("WebDriver已关闭")
            except Exception as e:
                logging.error(f"关闭WebDriver失败: {e}")
    
    def handle_cookies(self):
        """处理cookies弹窗"""
        try:
            # 等待并处理cookies弹窗
            accept_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '全部接受') or contains(text(), '接受') or contains(text(), 'Accept')]"))
            )
            accept_btn.click()
            logging.info("已接受cookies")
            time.sleep(2)
        except:
            logging.info("未发现cookies弹窗或已处理")

    def search_drivers(self):
        """搜索驱动程序"""
        driver = self.get_driver()

        try:
            logging.info(f"访问NVIDIA驱动页面: {self.url}")
            driver.get(self.url)

            # 处理cookies弹窗
            self.handle_cookies()

            # 等待页面完全加载
            wait = WebDriverWait(driver, 30)

            # 使用更精确的选择器找到搜索按钮
            search_selectors = [
                "//div[@id='lclStartSearchBtn']/..",  # 点击搜索按钮div的父元素
                "//span[contains(text(), '开始搜索')]/../..",  # 通过"开始搜索"文本找到父元素
                "//div[contains(text(), '开始搜索')]/..",  # 通过div文本找到父元素
                "//a[contains(@class, 'btn-manual') and .//text()[contains(., '开始搜索')]]",  # 包含"开始搜索"文本的btn-manual链接
                "/html/body/div[1]/div/div[2]/div/div/div/div/div[2]/div[2]/div/div[4]/div[1]/form/div[2]/div[8]/a"
            ]

            search_button = None
            for selector in search_selectors:
                try:
                    search_button = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    logging.info(f"找到搜索按钮: {selector}")
                    break
                except Exception as e:
                    logging.debug(f"选择器失败 {selector}: {e}")
                    continue

            if not search_button:
                logging.error("未找到搜索按钮")
                return []

            logging.info("点击开始搜索按钮")
            search_button.click()

            # 等待更长时间让JavaScript执行和驱动列表加载
            logging.info("等待驱动列表加载...")
            time.sleep(15)  # 增加等待时间

            # 等待搜索结果容器出现并包含内容
            try:
                # 首先等待容器出现
                wait.until(EC.presence_of_element_located((By.ID, "betaSearchResults")))

                # 然后等待容器内有实际内容
                wait.until(lambda driver: len(driver.find_elements(By.CSS_SELECTOR, "#betaSearchResults .driverBox")) > 0)
                logging.info("驱动列表已加载")
            except TimeoutException:
                logging.warning("等待驱动列表超时，尝试继续...")

            return self.extract_driver_info()

        except TimeoutException:
            logging.error("页面加载超时或未找到搜索按钮")
            return []
        except Exception as e:
            logging.error(f"搜索驱动失败: {e}")
            return []
    
    def extract_driver_info(self):
        """提取驱动信息"""
        driver = self.get_driver()
        drivers = []

        try:
            # 根据实际HTML结构，查找驱动容器
            # 首先等待搜索结果容器出现
            wait = WebDriverWait(driver, 15)

            # 等待驱动搜索结果容器
            try:
                wait.until(EC.presence_of_element_located((By.ID, "betaSearchResults")))
                logging.info("找到驱动搜索结果容器")
            except:
                # 备用等待方式
                wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".driverBox")))
                logging.info("找到驱动盒子容器")

            # 查找所有驱动盒子
            driver_boxes = driver.find_elements(By.CSS_SELECTOR, ".driverBox")

            if not driver_boxes:
                # 备用选择器
                driver_boxes = driver.find_elements(By.XPATH, "//div[contains(@class, 'driverBox')]")

            logging.info(f"找到 {len(driver_boxes)} 个驱动项")

            for box in driver_boxes:
                try:
                    # 提取驱动名称 - 正确的选择器是 a.drvrTitle
                    name_selectors = ["a.drvrTitle", ".drvrTitle", ".detailTitle a"]
                    name = ""
                    for selector in name_selectors:
                        try:
                            name_elem = box.find_element(By.CSS_SELECTOR, selector)
                            name = name_elem.text.strip()
                            if name:
                                break
                        except:
                            continue

                    # 提取版本信息 - 正确的选择器是 .versnDate
                    version_selectors = [".versnDate", ".longdesc"]
                    version_text = ""
                    for selector in version_selectors:
                        try:
                            desc_elem = box.find_element(By.CSS_SELECTOR, selector)
                            version_text = desc_elem.text.strip()
                            if version_text:
                                break
                        except:
                            continue

                    # 解析版本号和日期
                    version = ""
                    release_date = ""

                    if version_text:
                        # 处理格式如: "Version: 430.64 - Release Date: Thu May 09, 2019"
                        if "Version:" in version_text and "Release Date:" in version_text:
                            parts = version_text.split(" - ")
                            if len(parts) >= 2:
                                version = parts[0].replace("Version:", "").strip()
                                release_date = parts[1].replace("Release Date:", "").strip()
                        # 处理中文格式
                        elif "驱动程序版本:" in version_text:
                            lines = version_text.split('\n')
                            for i, line in enumerate(lines):
                                line = line.strip()
                                if "驱动程序版本:" in line:
                                    if i + 1 < len(lines):
                                        version = lines[i + 1].strip()
                                elif "发行日期:" in line:
                                    if i + 1 < len(lines):
                                        release_date = lines[i + 1].strip()

                    # 查找下载按钮
                    download_selectors = ["a.link-btn", ".link-btn", ".drvDwn a"]
                    download_btn = None
                    for selector in download_selectors:
                        try:
                            download_btn = box.find_element(By.CSS_SELECTOR, selector)
                            if download_btn:
                                break
                        except:
                            continue

                    if name and download_btn:
                        driver_info = {
                            'name': name,
                            'version': version if version else "未知版本",
                            'release_date': release_date if release_date else "未知日期",
                            'download_element': download_btn,
                            'found_time': datetime.now().isoformat()
                        }
                        drivers.append(driver_info)
                        logging.info(f"找到驱动: {name} - 版本: {version} - 日期: {release_date}")

                except Exception as e:
                    logging.warning(f"提取驱动信息失败: {e}")
                    continue

            return drivers

        except Exception as e:
            logging.error(f"提取驱动信息失败: {e}")
            return []
    
    def download_driver(self, driver_info):
        """下载驱动程序"""
        try:
            version = driver_info['version']
            if version in self.downloaded_drivers:
                logging.info(f"驱动 {version} 已下载过，跳过")
                return False

            logging.info(f"开始下载驱动: {driver_info['name']} - {version}")

            # 点击下载按钮
            download_btn = driver_info['download_element']
            download_btn.click()

            # 等待可能的页面跳转和下载开始
            time.sleep(15)

            # 检查是否有新的下载页面或直接下载
            driver = self.get_driver()
            current_url = driver.current_url

            # 如果跳转到了下载页面，查找最终下载链接
            if 'download' in current_url.lower():
                try:
                    # 查找最终下载按钮
                    final_download_btn = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//a[contains(@href, '.exe') or contains(text(), '下载')]"))
                    )
                    final_download_btn.click()
                    time.sleep(10)
                except:
                    logging.warning("未找到最终下载链接，可能已开始下载")

            # 记录下载信息
            self.downloaded_drivers[version] = {
                'name': driver_info['name'],
                'version': version,
                'release_date': driver_info['release_date'],
                'download_time': datetime.now().isoformat()
            }

            self.save_downloaded_drivers()
            logging.info(f"驱动下载已启动: {version}")
            return True

        except Exception as e:
            logging.error(f"下载驱动失败: {e}")
            return False
    
    def check_for_updates(self):
        """检查驱动更新"""
        logging.info("开始检查NVIDIA驱动更新...")
        
        try:
            drivers = self.search_drivers()
            
            if not drivers:
                logging.warning("未找到任何驱动")
                return
            
            new_drivers = []
            for driver_info in drivers:
                version = driver_info['version']
                if version not in self.downloaded_drivers:
                    new_drivers.append(driver_info)
            
            if not new_drivers:
                logging.info("没有新的驱动更新")
                return
            
            logging.info(f"发现 {len(new_drivers)} 个新驱动")
            
            # 下载新驱动
            for driver_info in new_drivers:
                self.download_driver(driver_info)
                time.sleep(5)  # 下载间隔
            
        except Exception as e:
            logging.error(f"检查更新失败: {e}")
    
    def run_once(self):
        """运行一次检查"""
        try:
            self.check_for_updates()
        finally:
            self.close_driver()
    
    def run_continuous(self, interval_hours=6):
        """持续监控"""
        logging.info(f"开始持续监控NVIDIA驱动，检查间隔: {interval_hours} 小时")
        
        try:
            while True:
                try:
                    self.check_for_updates()
                    logging.info(f"等待 {interval_hours} 小时后进行下次检查...")
                    time.sleep(interval_hours * 3600)
                except KeyboardInterrupt:
                    logging.info("监控已停止")
                    break
                except Exception as e:
                    logging.error(f"监控过程中出错: {e}")
                    time.sleep(300)  # 出错后等待5分钟
        finally:
            self.close_driver()

if __name__ == "__main__":
    monitor = NvidiaDriverMonitor()
    
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        monitor.run_once()
    else:
        monitor.run_continuous(interval_hours=6)
