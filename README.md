# 移动平台版本监控与AI分析系统

自动监控Apple和Android开发者平台版本更新，并使用AI分析Release Notes的完整解决方案。

## 🚀 功能特性

### 📱 多平台支持
- **Apple平台**：监控Apple Developer RSS更新
- **Android平台**：监控Android Developer版本页面更新
- **统一架构**：共享AI分析引擎和配置系统

### 🔄 版本监控模块
#### Apple监控 (apple_release_monitor.py)
- 🔄 自动监控Apple Developer RSS更新
- 📄 自动提取Release Notes内容
- 💾 本地保存Release Notes文件
- 🚫 避免重复处理已处理的releases

#### Android监控 (android_release_monitor.py)
- 🔄 自动监控Android版本页面更新
- 📄 自动提取行为变更(Behavior Changes)内容
- 💾 本地保存版本变更文件
- 🚫 避免重复处理已处理的版本

### 🤖 AI分析模块
- 🤖 基于LangChain的统一AI分析框架
- 📊 自动分析Release Notes和行为变更内容
- 🔧 支持多种AI模型 (OpenAI, Anthropic, Ollama)
- ⚙️ 完全可配置的提示词和参数
- 📁 实时监控新文件并自动分析
- 💾 多格式输出 (Markdown, JSON, TXT)
- 🎯 针对不同平台的专业化分析

## 📁 项目结构

```
version-notice/
├── 📄 监控脚本
│   ├── apple_release_monitor.py      # Apple平台RSS监控
│   ├── android_release_monitor.py    # Android平台版本监控
│   └── run_analysis.py              # 统一启动脚本
├── 🤖 AI分析模块
│   ├── analyzer_manager.py          # 分析管理器
│   ├── universal_analyzer.py        # 通用AI分析器
│   ├── config_reader.py             # 配置读取器
│   └── file_monitor.py              # 文件监控器
├── ⚙️ 配置文件
│   ├── config/
│   │   ├── config_ios.yaml          # iOS分析配置
│   │   └── config_android.yaml      # Android分析配置
│   └── requirements.txt             # 依赖包列表
├── 📊 数据目录
│   ├── notes/                       # 原始版本说明文件
│   │   ├── ios_notes/              # iOS版本说明
│   │   └── android_notes/          # Android版本说明
│   ├── analysis_results/            # AI分析结果
│   │   ├── ios/                    # iOS分析结果
│   │   └── android/                # Android分析结果
│   └── logs/                       # 日志文件
└── 📋 记录文件
    ├── processed_releases.json      # 已处理的Apple releases
    ├── processed_android_releases.json # 已处理的Android版本
    └── processed_analysis.json      # 已分析的文件记录
```

## 🛠️ 安装与配置

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置AI API
编辑对应平台的配置文件，设置你的API密钥：

**iOS配置 (config/config_ios.yaml)**
```yaml
ai_analysis:
  api:
    provider: "openai"  # 或 anthropic, ollama
    model: "gpt-4o-mini"
    api_key: "your-api-key-here"
    base_url: "https://api.openai.com/v1"  # 可选，自定义API端点
```

**Android配置 (config/config_android.yaml)**
```yaml
ai_analysis:
  api:
    provider: "openai"
    model: "gpt-4o-mini"
    api_key: "your-api-key-here"
    base_url: "https://api.openai.com/v1"
```

## 🚀 使用方法

### 方式一：统一启动（推荐）
同时运行iOS和Android的监控与分析：
```bash
python run_analysis.py
```

### 方式二：分平台运行

#### Apple平台
**运行一次检查**
```bash
python apple_release_monitor.py --once
```

**持续监控（默认1分钟检查一次）**
```bash
python apple_release_monitor.py
```

#### Android平台
**运行一次检查**
```bash
python android_release_monitor.py --once
```

**持续监控（默认1分钟检查一次）**
```bash
python android_release_monitor.py
```

#### AI分析
**iOS分析**
```bash
python analyzer_manager.py config/config_ios.yaml
```

**Android分析**
```bash
python analyzer_manager.py config/config_android.yaml
```

## 📂 输出文件说明

### 监控输出
#### Apple平台
- `notes/ios_notes/` - Apple Release Notes文件保存目录
- `processed_releases.json` - 已处理的Apple releases记录
- `logs/apple_releases.log` - Apple RSS监控日志

#### Android平台
- `notes/android_notes/` - Android版本说明文件保存目录
- `processed_android_releases.json` - 已处理的Android版本记录
- `logs/android_releases.log` - Android版本监控日志

### AI分析输出
- `analysis_results/ios/` - iOS AI分析结果保存目录
- `analysis_results/android/` - Android AI分析结果保存目录
- `processed_analysis.json` - 已分析文件记录
- `logs/ios_ai_analysis.log` - iOS AI分析日志
- `logs/android_ai_analysis.log` - Android AI分析日志

## 📝 文件命名规则

### Apple Release Notes
文件命名格式：`YYYYMMDD_HHMMSS_产品名称.txt`
例如：`20250825_143022_Xcode_26_beta_6.txt`

### Android版本说明
文件命名格式：`android{版本号}_notes.txt`
例如：`android15_notes.txt`、`android14_notes.txt`

## ⚙️ 详细配置说明

配置文件分为iOS和Android两个独立的YAML文件，支持以下配置选项：

### AI分析配置 (ai_analysis)
```yaml
ai_analysis:
  api:
    provider: "openai"              # AI提供商：openai, anthropic, ollama
    model: "gpt-4o-mini"           # 模型名称
    api_key: "your-api-key"        # API密钥
    base_url: "https://api.openai.com/v1"  # API端点（可选）
    temperature: 0.3               # 创造性参数 (0-1)
    max_tokens: 20000             # 最大输出token数
    timeout: 60                   # 请求超时时间（秒）

  prompts:
    system_prompt: |              # 系统提示词（针对平台定制）
      你是一个专业的iOS/Android开发者工具分析师...
    user_prompt_template: |       # 用户提示词模板
      请分析以下Release Notes...

  output:
    save_analysis: true           # 是否保存分析结果
    analysis_dir: "analysis_results/ios"  # 分析结果保存目录
    format: "markdown"            # 输出格式：markdown, json, txt
    include_timestamp: true       # 是否包含时间戳
    include_original_filename: true  # 是否包含原文件名
```

### 监控配置 (monitoring)
```yaml
monitoring:
  watch_directory: "notes/ios_notes"  # 监控目录
  check_interval: 30              # 检查间隔（秒）
  file_patterns:                  # 文件匹配模式
    - "*.txt"
    - "*.md"
  ignore_patterns:                # 忽略的文件模式
    - ".*"                        # 隐藏文件
    - "*_analyzed*"               # 已分析的文件
  processed_files_db: "processed_analysis.json"  # 处理记录文件
```

### 高级配置 (advanced)
```yaml
advanced:
  max_concurrent_analysis: 3      # 最大并发分析数
  max_retries: 3                 # 最大重试次数
  retry_delay: 5                 # 重试延迟（秒）
  max_file_size: 1048576         # 最大文件大小（字节）
  enable_cache: true             # 启用缓存
  cache_duration_hours: 24       # 缓存持续时间（小时）
```

## 🔄 工作流程

### 完整流程图
```
┌─────────────────┐    ┌─────────────────┐
│   Apple RSS     │    │  Android 版本   │
│     监控        │    │     监控        │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│  提取 Release   │    │  提取行为变更   │
│     Notes       │    │     内容        │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│ 保存到本地文件  │    │ 保存到本地文件  │
│ notes/ios_notes │    │notes/android_notes│
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     ▼
          ┌─────────────────┐
          │   文件监控器    │
          │  (File Monitor) │
          └─────────┬───────┘
                    ▼
          ┌─────────────────┐
          │   AI 分析器     │
          │ (Universal AI)  │
          └─────────┬───────┘
                    ▼
          ┌─────────────────┐
          │  生成分析报告   │
          │analysis_results │
          └─────────────────┘
```

### Apple平台监控流程
1. **RSS获取**：定期检查Apple Developer RSS
2. **解析更新**：解析新的release项目
3. **内容提取**：访问release页面，提取Release Notes链接
4. **下载内容**：下载并提取Release Notes完整内容
5. **本地保存**：保存到 `notes/ios_notes/` 目录
6. **记录跟踪**：记录已处理的releases避免重复

### Android平台监控流程
1. **版本页面**：访问Android Developer版本页面
2. **解析版本**：解析所有可用的Android版本
3. **行为变更**：访问每个版本的行为变更页面
4. **内容提取**：提取行为变更的详细内容
5. **本地保存**：保存到 `notes/android_notes/` 目录
6. **记录跟踪**：记录已处理的版本避免重复

### AI分析流程
1. **文件监控**：实时监控 `notes/` 目录下的新文件
2. **内容读取**：检测到新文件时读取内容
3. **AI分析**：调用配置的AI模型进行专业分析
4. **结果生成**：生成结构化的Markdown分析报告
5. **保存输出**：保存到对应平台的 `analysis_results/` 目录
6. **记录管理**：记录已分析文件避免重复处理

## ✨ 程序特点

- **🔄 多平台支持**：同时支持Apple和Android平台监控
- **🚫 避免重复**：智能跟踪已处理的版本和文件，避免重复工作
- **⚡ 高效设计**：使用session复用连接，合理的错误处理和重试机制
- **📖 可读性强**：清晰的类结构和函数命名，详细的中文注释
- **⚙️ 完全可配置**：所有参数都可通过YAML配置文件调整，无需修改代码
- **🤖 AI驱动**：基于LangChain框架，支持多种AI模型和自定义提示词
- **📊 结构化输出**：生成专业的Markdown格式分析报告
- **🔍 实时监控**：支持文件系统监控，新文件自动触发分析

## 📊 AI分析输出示例

AI分析会根据不同平台提供专业的结构化信息：

### iOS平台分析输出
```markdown
# iOS 17.2 Release Notes 分析报告

## 版本信息
- **产品名称**: iOS
- **版本号**: 17.2
- **发布日期**: 2024年1月15日

## 兼容性变化
- ✅ 向后兼容，无破坏性变化
- ⚠️ 部分API行为优化，建议测试

## 开发者影响
1. **新增API**: 新增隐私控制相关API
2. **性能优化**: Core Data查询性能提升20%
3. **安全增强**: 增强的应用沙盒机制

## 推荐行动
1. 更新Xcode到最新版本
2. 测试应用在新系统上的兼容性
3. 考虑采用新的隐私API
```

### Android平台分析输出
```markdown
# Android 15 行为变更分析报告

## 版本信息
- **产品名称**: Android
- **版本号**: 15 (API Level 35)
- **发布日期**: 2024年2月1日

## 兼容性变化
- ⚠️ 目标SDK 35的应用需要适配新的权限模型
- 🔄 后台服务启动限制更加严格

## 开发者影响
1. **权限变更**: 新增细粒度位置权限
2. **性能限制**: 后台应用CPU使用限制
3. **安全增强**: 增强的应用签名验证

## 推荐行动
1. 更新targetSdkVersion到35
2. 适配新的权限请求流程
3. 优化后台服务使用
```

## 🔧 开发与扩展

### 添加新的AI模型
1. 在配置文件中修改 `provider` 和 `model` 参数
2. 如需要，添加对应的API配置

### 自定义分析提示词
1. 编辑配置文件中的 `system_prompt` 和 `user_prompt_template`
2. 根据需要调整分析重点和输出格式

### 添加新的监控源
1. 参考现有的监控脚本结构
2. 实现新的监控类
3. 在 `run_analysis.py` 中添加对应的启动逻辑

## ⚠️ 注意事项

### 网络和API
- 程序会自动处理Apple的重定向链接和Android的动态内容
- 支持多种Release Notes页面格式的解析
- 网络请求包含适当的超时和重试机制
- AI API调用包含错误处理和重试逻辑

### 配置和使用
- 确保AI API密钥配置正确且有足够的配额
- 首次运行可能需要较长时间来处理历史版本
- 建议定期检查日志文件以监控运行状态
- 可以通过配置文件调整检查频率和分析参数

### 系统要求
- Python 3.8+
- 稳定的网络连接
- 足够的磁盘空间存储版本文件和分析结果

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
