#!/usr/bin/env python3
"""
NVIDIA页面调试脚本
用于分析页面结构和元素定位
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def debug_nvidia_page():
    """调试NVIDIA页面结构"""
    
    # 设置Chrome驱动
    options = Options()
    options.add_argument('--window-size=1920,1080')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    try:
        url = "https://www.nvidia.cn/geforce/drivers/"
        logging.info(f"访问页面: {url}")
        driver.get(url)
        
        # 处理cookies
        try:
            accept_btn = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '全部接受')]"))
            )
            accept_btn.click()
            logging.info("已处理cookies")
            time.sleep(2)
        except:
            logging.info("未发现cookies弹窗")
        
        # 点击搜索按钮
        search_selectors = [
            "#lclStartSearchBtn",
            "//div[@id='lclStartSearchBtn']",
            "//span[contains(text(), '开始搜索')]/.."
        ]
        
        search_btn = None
        for selector in search_selectors:
            try:
                if selector.startswith('#'):
                    search_btn = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                else:
                    search_btn = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                logging.info(f"✓ 找到搜索按钮: {selector}")
                break
            except:
                logging.warning(f"✗ 搜索按钮选择器失败: {selector}")
        
        if search_btn:
            search_btn.click()
            logging.info("已点击搜索按钮")
            
            # 等待结果加载
            time.sleep(15)
            
            # 检查搜索结果容器
            try:
                results_container = driver.find_element(By.ID, "betaSearchResults")
                logging.info(f"✓ 找到搜索结果容器: {results_container.tag_name}")
                logging.info(f"容器内容长度: {len(results_container.text)}")
                
                # 查找驱动盒子
                driver_boxes = results_container.find_elements(By.CSS_SELECTOR, ".driverBox")
                logging.info(f"✓ 找到 {len(driver_boxes)} 个驱动盒子")
                
                # 分析前几个驱动盒子的结构
                for i, box in enumerate(driver_boxes[:3]):
                    logging.info(f"\n=== 驱动盒子 {i+1} ===")
                    
                    # 查找所有可能的标题元素
                    title_selectors = ["a.drvrTitle", ".drvrTitle", "h3", ".detailTitle", ".detailTitle a"]
                    for selector in title_selectors:
                        try:
                            elem = box.find_element(By.CSS_SELECTOR, selector)
                            logging.info(f"  标题 ({selector}): {elem.text.strip()}")
                        except:
                            logging.info(f"  标题 ({selector}): 未找到")
                    
                    # 查找所有可能的版本元素
                    version_selectors = [".versnDate", ".longdesc", ".desc"]
                    for selector in version_selectors:
                        try:
                            elem = box.find_element(By.CSS_SELECTOR, selector)
                            text = elem.text.strip()
                            if text:
                                logging.info(f"  版本 ({selector}): {text}")
                        except:
                            logging.info(f"  版本 ({selector}): 未找到")
                    
                    # 查找下载按钮
                    download_selectors = ["a.link-btn", ".link-btn", ".drvDwn a"]
                    for selector in download_selectors:
                        try:
                            elem = box.find_element(By.CSS_SELECTOR, selector)
                            logging.info(f"  下载按钮 ({selector}): 找到")
                            logging.info(f"    href: {elem.get_attribute('href')}")
                            logging.info(f"    text: {elem.text.strip()}")
                        except:
                            logging.info(f"  下载按钮 ({selector}): 未找到")
                    
                    # 输出完整的HTML结构（前200字符）
                    html = box.get_attribute('outerHTML')
                    logging.info(f"  HTML结构: {html[:200]}...")
                
            except Exception as e:
                logging.error(f"分析搜索结果失败: {e}")
                
                # 尝试查找页面上的所有驱动相关元素
                logging.info("尝试查找页面上的所有相关元素...")
                
                all_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'GeForce') or contains(text(), 'Driver') or contains(text(), '驱动')]")
                logging.info(f"找到 {len(all_elements)} 个包含驱动相关文本的元素")
                
                for i, elem in enumerate(all_elements[:10]):
                    try:
                        logging.info(f"  元素 {i+1}: {elem.tag_name}.{elem.get_attribute('class')} - {elem.text.strip()[:50]}")
                    except:
                        pass
        
        else:
            logging.error("未找到搜索按钮")
            
    except Exception as e:
        logging.error(f"调试失败: {e}")
    finally:
        input("按回车键关闭浏览器...")
        driver.quit()

if __name__ == "__main__":
    debug_nvidia_page()
