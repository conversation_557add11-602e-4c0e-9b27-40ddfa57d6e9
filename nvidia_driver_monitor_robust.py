#!/usr/bin/env python3
"""
NVIDIA驱动更新监控程序 - 健壮版
专门针对 https://www.nvidia.cn/geforce/drivers/ 优化
处理cookies弹窗和精确的元素定位
"""

import os
import time
import json
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nvidia_monitor.log'),
        logging.StreamHandler()
    ]
)

class NvidiaDriverMonitor:
    def __init__(self):
        self.url = "https://www.nvidia.cn/geforce/drivers/"
        self.download_dir = "drivers"
        self.data_file = "nvidia_versions.json"
        self.driver = None
        
        os.makedirs(self.download_dir, exist_ok=True)
        self.downloaded_versions = self.load_versions()
    
    def load_versions(self):
        """加载已下载版本记录"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                return set(json.load(f))
        except:
            return set()
    
    def save_versions(self):
        """保存版本记录"""
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump(list(self.downloaded_versions), f, ensure_ascii=False, indent=2)
    
    def setup_driver(self):
        """设置Chrome驱动"""
        if self.driver:
            return self.driver
            
        options = Options()
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 设置下载目录
        prefs = {
            "download.default_directory": os.path.abspath(self.download_dir),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        options.add_experimental_option("prefs", prefs)
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=options)
        
        # 隐藏自动化标识
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return self.driver
    
    def handle_cookies(self):
        """处理cookies弹窗"""
        try:
            # 多种可能的cookies按钮文本
            cookie_selectors = [
                "//button[contains(text(), '全部接受')]",
                "//button[contains(text(), '接受全部')]", 
                "//button[contains(text(), '接受')]",
                "//button[contains(text(), 'Accept All')]",
                "//button[contains(text(), 'Accept')]",
                "//div[contains(@class, 'cookie')]//button",
                "//*[@id='onetrust-accept-btn-handler']"
            ]
            
            for selector in cookie_selectors:
                try:
                    accept_btn = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    accept_btn.click()
                    logging.info(f"已接受cookies (使用选择器: {selector})")
                    time.sleep(2)
                    return
                except:
                    continue
                    
            logging.info("未发现cookies弹窗")
        except Exception as e:
            logging.info(f"处理cookies时出错: {e}")
    
    def click_search_button(self):
        """点击搜索按钮"""
        search_selectors = [
            # 你提供的精确XPath
            "/html/body/div[1]/div/div[2]/div/div/div/div/div[2]/div[2]/div/div[4]/div[1]/form/div[2]/div[8]/a/div",
            # 备用选择器
            "//a[contains(@class, 'btn') and contains(., '开始搜索')]",
            "//button[contains(text(), '开始搜索')]",
            "//a[contains(text(), '开始搜索')]",
            "//*[contains(text(), '开始搜索')]",
            "//div[contains(text(), '开始搜索')]"
        ]
        
        for selector in search_selectors:
            try:
                search_btn = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                search_btn.click()
                logging.info(f"成功点击搜索按钮 (使用选择器: {selector})")
                return True
            except:
                continue
        
        logging.error("未找到搜索按钮")
        return False
    
    def wait_for_drivers(self):
        """等待驱动列表加载"""
        driver_selectors = [
            "//div[contains(text(), 'GeForce Game Ready')]",
            "//div[contains(text(), 'Game Ready')]", 
            "//div[contains(@class, 'driver')]",
            "//*[contains(text(), '驱动程序版本')]"
        ]
        
        logging.info("等待驱动列表加载...")
        time.sleep(8)  # 固定等待时间
        
        for selector in driver_selectors:
            try:
                WebDriverWait(self.driver, 20).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                logging.info(f"驱动列表已加载 (检测到: {selector})")
                return True
            except:
                continue
        
        logging.warning("驱动列表可能未完全加载")
        return False
    
    def extract_drivers(self):
        """提取驱动信息"""
        drivers = []
        
        try:
            # 查找包含驱动信息的容器
            driver_containers = self.driver.find_elements(By.XPATH, "//div[contains(text(), 'GeForce Game Ready')]/..")
            
            if not driver_containers:
                # 备用查找方式
                driver_containers = self.driver.find_elements(By.XPATH, "//*[contains(text(), '驱动程序版本')]/../..")
            
            logging.info(f"找到 {len(driver_containers)} 个驱动容器")
            
            for container in driver_containers:
                try:
                    # 提取驱动名称
                    name_elem = container.find_element(By.XPATH, ".//div[contains(text(), 'GeForce Game Ready')]")
                    name = name_elem.text.strip()
                    
                    # 提取版本信息
                    version_elem = container.find_element(By.XPATH, ".//div[contains(text(), '驱动程序版本')]")
                    version_text = version_elem.text.strip()
                    
                    # 解析版本号
                    version = ""
                    if "驱动程序版本:" in version_text:
                        version = version_text.split("驱动程序版本:")[1].split("-")[0].strip()
                    
                    # 查找下载按钮
                    download_selectors = [
                        ".//button[contains(text(), '获取下载')]",
                        ".//a[contains(text(), '获取下载')]", 
                        ".//button[contains(text(), '下载')]",
                        ".//a[contains(text(), '下载')]"
                    ]
                    
                    download_btn = None
                    for selector in download_selectors:
                        try:
                            download_btn = container.find_element(By.XPATH, selector)
                            break
                        except:
                            continue
                    
                    if version and download_btn:
                        drivers.append({
                            'name': name,
                            'version': version,
                            'download_btn': download_btn,
                            'container': container
                        })
                        logging.info(f"发现驱动: {name} - 版本: {version}")
                
                except Exception as e:
                    logging.warning(f"解析驱动信息失败: {e}")
                    continue
            
            return drivers
            
        except Exception as e:
            logging.error(f"提取驱动信息失败: {e}")
            return []
    
    def download_driver(self, driver_info):
        """下载单个驱动"""
        version = driver_info['version']
        
        if version in self.downloaded_versions:
            logging.info(f"版本 {version} 已下载，跳过")
            return False
        
        try:
            logging.info(f"开始下载: {driver_info['name']} - {version}")
            
            # 点击下载按钮
            driver_info['download_btn'].click()
            
            # 等待页面跳转或下载开始
            time.sleep(10)
            
            # 尝试处理下载页面
            try:
                # 查找最终下载链接
                final_download_selectors = [
                    "//a[contains(@href, '.exe')]",
                    "//button[contains(text(), '下载')]",
                    "//a[contains(text(), '立即下载')]"
                ]
                
                for selector in final_download_selectors:
                    try:
                        final_btn = WebDriverWait(self.driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        final_btn.click()
                        logging.info("点击了最终下载按钮")
                        break
                    except:
                        continue
                        
            except:
                logging.info("可能已开始下载或无需额外操作")
            
            # 记录已下载版本
            self.downloaded_versions.add(version)
            self.save_versions()
            
            logging.info(f"驱动 {version} 下载已启动")
            return True
            
        except Exception as e:
            logging.error(f"下载驱动 {version} 失败: {e}")
            return False
    
    def check_updates(self):
        """检查并下载更新"""
        try:
            driver = self.setup_driver()
            
            logging.info("访问NVIDIA驱动页面...")
            driver.get(self.url)
            
            # 处理cookies
            self.handle_cookies()
            
            # 点击搜索按钮
            if not self.click_search_button():
                return
            
            # 等待驱动列表加载
            if not self.wait_for_drivers():
                logging.warning("驱动列表可能未完全加载，继续尝试...")
            
            # 提取驱动信息
            drivers = self.extract_drivers()
            
            if not drivers:
                logging.warning("未找到任何驱动")
                return
            
            # 下载新驱动
            new_downloads = 0
            for driver_info in drivers:
                if self.download_driver(driver_info):
                    new_downloads += 1
                    # 返回主页面继续下载其他驱动
                    if len(drivers) > 1:
                        driver.back()
                        time.sleep(3)
            
            if new_downloads > 0:
                logging.info(f"成功下载 {new_downloads} 个新驱动")
            else:
                logging.info("没有新的驱动更新")
                
        except Exception as e:
            logging.error(f"检查更新失败: {e}")
        finally:
            if self.driver:
                self.driver.quit()
                self.driver = None
    
    def run_once(self):
        """运行一次检查"""
        logging.info("开始检查NVIDIA驱动更新...")
        self.check_updates()
        logging.info("检查完成")
    
    def run_monitor(self, hours=6):
        """持续监控模式"""
        logging.info(f"开始监控模式，每 {hours} 小时检查一次")
        
        while True:
            try:
                self.check_updates()
                logging.info(f"等待 {hours} 小时后进行下次检查...")
                time.sleep(hours * 3600)
            except KeyboardInterrupt:
                logging.info("监控已停止")
                break
            except Exception as e:
                logging.error(f"监控出错: {e}")
                time.sleep(300)  # 出错后等待5分钟

def main():
    monitor = NvidiaDriverMonitor()
    
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        monitor.run_once()
    else:
        monitor.run_monitor(hours=6)

if __name__ == "__main__":
    main()
