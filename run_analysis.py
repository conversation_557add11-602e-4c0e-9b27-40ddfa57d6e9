#!/usr/bin/env python3
"""
启动脚本 - 同时运行iOS和Android的RSS监控和AI分析
"""

import subprocess
import sys
import time
import threading
import logging
from pathlib import Path

def setup_logging():
    """设置日志"""
    import os
    os.makedirs('logs', exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/run_analysis.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def run_apple_rss_monitor():
    """运行Apple RSS监控"""
    try:
        logging.info("启动Apple RSS监控...")
        subprocess.run([sys.executable, "apple_release_monitor.py"], check=True)
    except Exception as e:
        logging.error(f"Apple RSS监控出错: {e}")

def run_android_rss_monitor():
    """运行Android RSS监控"""
    try:
        logging.info("启动Android RSS监控...")
        subprocess.run([sys.executable, "android_release_monitor.py"], check=True)
    except Exception as e:
        logging.error(f"Android RSS监控出错: {e}")

def run_ios_ai_analyzer():
    """运行iOS AI分析器"""
    try:
        logging.info("启动iOS AI分析器...")
        subprocess.run([sys.executable, "analyzer_manager.py", "config/config_ios.yaml"], check=True)
    except Exception as e:
        logging.error(f"iOS AI分析器出错: {e}")

def run_android_ai_analyzer():
    """运行Android AI分析器"""
    try:
        logging.info("启动Android AI分析器...")
        subprocess.run([sys.executable, "analyzer_manager.py", "config/config_android.yaml"], check=True)
    except Exception as e:
        logging.error(f"Android AI分析器出错: {e}")

def main():
    """主函数"""
    setup_logging()

    # 检查必要文件
    required_files = [
        "apple_release_monitor.py",
        "android_release_monitor.py",
        "analyzer_manager.py",
        "config/config_ios.yaml",
        "config/config_android.yaml"
    ]
    for file in required_files:
        if not Path(file).exists():
            logging.error(f"缺少必要文件: {file}")
            return

    logging.info("开始启动iOS和Android Developer Release监控和AI分析系统...")

    # 创建线程
    apple_rss_thread = threading.Thread(target=run_apple_rss_monitor, daemon=True)
    android_rss_thread = threading.Thread(target=run_android_rss_monitor, daemon=True)
    ios_ai_thread = threading.Thread(target=run_ios_ai_analyzer, daemon=True)
    android_ai_thread = threading.Thread(target=run_android_ai_analyzer, daemon=True)

    try:
        # 启动RSS监控线程
        logging.info("启动RSS监控线程...")
        apple_rss_thread.start()
        android_rss_thread.start()

        time.sleep(3)  # 稍等一下再启动AI分析器

        # 启动AI分析线程
        logging.info("启动AI分析线程...")
        ios_ai_thread.start()
        android_ai_thread.start()

        logging.info("所有服务已启动，按Ctrl+C停止...")

        # 等待线程
        threads = [apple_rss_thread, android_rss_thread, ios_ai_thread, android_ai_thread]
        while True:
            time.sleep(1)
            # 检查是否所有线程都已结束
            if all(not thread.is_alive() for thread in threads):
                break

    except KeyboardInterrupt:
        logging.info("正在停止服务...")
    except Exception as e:
        logging.error(f"运行出错: {e}")

if __name__ == "__main__":
    main()
