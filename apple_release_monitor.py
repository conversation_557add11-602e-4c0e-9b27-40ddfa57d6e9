#!/usr/bin/env python3
"""
Apple Developer Release Monitor
监控Apple Developer RSS并自动提取Release Notes
"""

import requests
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup
import time
import json
import os
from datetime import datetime
from urllib.parse import urljoin, urlparse
import logging

# Selenium相关导入
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    logging.warning("Selenium或webdriver-manager未安装，将使用基础的requests方式（可能无法获取JavaScript渲染的内容）")

# 配置日志
import os
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/apple_releases.log'),
        logging.StreamHandler()
    ]
)

class AppleReleaseMonitor:
    def __init__(self):
        self.rss_url = "https://developer.apple.com/news/releases/rss/releases.rss"
        self.base_url = "https://developer.apple.com"
        self.data_file = "processed_releases.json"
        self.notes_dir = "notes/ios_notes"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 创建目录
        os.makedirs(self.notes_dir, exist_ok=True)
        
        # 加载已处理的releases
        self.processed_releases = self.load_processed_releases()

        # Selenium配置
        self.use_selenium = SELENIUM_AVAILABLE
        self.driver = None
    
    def load_processed_releases(self):
        """加载已处理的releases记录"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return set(json.load(f))
            except:
                return set()
        return set()
    
    def save_processed_releases(self):
        """保存已处理的releases记录"""
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump(list(self.processed_releases), f, indent=2)

    def get_selenium_driver(self):
        """获取Selenium WebDriver"""
        if not SELENIUM_AVAILABLE:
            return None

        if self.driver is None:
            try:
                chrome_options = Options()
                chrome_options.add_argument('--headless')  # 无头模式
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--window-size=1920,1080')
                chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

                # 使用webdriver-manager自动管理ChromeDriver
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logging.info("Selenium WebDriver初始化成功")
            except Exception as e:
                logging.error(f"Selenium WebDriver初始化失败: {e}")
                self.use_selenium = False
                return None

        return self.driver

    def close_selenium_driver(self):
        """关闭Selenium WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
                logging.info("Selenium WebDriver已关闭")
            except Exception as e:
                logging.error(f"关闭Selenium WebDriver失败: {e}")
    
    def fetch_rss(self):
        """获取RSS内容"""
        try:
            response = self.session.get(self.rss_url, timeout=30)
            response.raise_for_status()
            return response.text
        except Exception as e:
            logging.error(f"获取RSS失败: {e}")
            return None
    
    def parse_rss(self, rss_content):
        """解析RSS内容"""
        try:
            root = ET.fromstring(rss_content)
            items = []
            
            for item in root.findall('.//item'):
                title = item.find('title').text if item.find('title') is not None else ""
                link = item.find('link').text if item.find('link') is not None else ""
                guid = item.find('guid').text if item.find('guid') is not None else ""
                pub_date = item.find('pubDate').text if item.find('pubDate') is not None else ""
                
                items.append({
                    'title': title,
                    'link': link,
                    'guid': guid,
                    'pub_date': pub_date
                })
            
            return items
        except Exception as e:
            logging.error(f"解析RSS失败: {e}")
            return []
    
    def extract_release_notes_url(self, release_page_url):
        """从release页面提取release notes链接"""
        try:
            response = self.session.get(release_page_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找"View release notes"链接
            release_notes_link = soup.find('a', string=lambda text: text and 'release notes' in text.lower())
            if not release_notes_link:
                # 尝试其他可能的选择器
                release_notes_link = soup.find('a', href=lambda href: href and 'rn' in href)
            
            if release_notes_link:
                href = release_notes_link.get('href')
                if href:
                    if href.startswith('/go/?id='):
                        # 处理重定向链接
                        redirect_url = urljoin(self.base_url, href)
                        redirect_response = self.session.get(redirect_url, timeout=30, allow_redirects=True)
                        return redirect_response.url
                    else:
                        return urljoin(self.base_url, href)
            
            return None
        except Exception as e:
            logging.error(f"提取release notes链接失败 {release_page_url}: {e}")
            return None
    
    def extract_release_notes_content_selenium(self, notes_url):
        """使用Selenium提取JavaScript渲染的release notes内容"""
        if not self.use_selenium:
            return None

        driver = self.get_selenium_driver()
        if not driver:
            return None

        try:
            logging.info(f"使用Selenium提取内容: {notes_url}")

            # 访问页面
            driver.get(notes_url)

            # 等待页面加载完成
            wait = WebDriverWait(driver, 20)

            # 等待主要内容加载
            try:
                # 等待标题或主要内容区域出现
                wait.until(EC.any_of(
                    EC.presence_of_element_located((By.TAG_NAME, "main")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".documentation-content")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, "article")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, "#app")),
                ))

                # 额外等待确保内容完全加载
                time.sleep(3)

            except TimeoutException:
                logging.warning("等待页面加载超时，尝试提取当前内容")

            # 获取页面源码
            page_source = driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            # 检查页面标题
            title_elem = soup.find('title')
            if title_elem:
                page_title = title_elem.get_text(strip=True)
                logging.info(f"页面标题: {page_title}")

                # 检查是否是错误页面
                if 'error' in page_title.lower() or 'not found' in page_title.lower():
                    logging.warning(f"页面可能出错: {page_title}")
                    return None

            # 移除不需要的元素
            for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside', 'noscript']):
                element.decompose()

            # 尝试找到主要内容区域
            content_selectors = [
                'main',
                'article',
                '.documentation-content',
                '.main-content',
                '.content',
                '.release-notes',
                '.devsite-article-body',
                '.devsite-main-content',
                '#main-content',
                '.page-content',
                '.article-content',
                '#app',
                'body'
            ]

            content = None
            for selector in content_selectors:
                content = soup.select_one(selector)
                if content:
                    logging.info(f"使用选择器找到内容: {selector}")
                    break

            if not content:
                content = soup
                logging.info("使用整个页面作为内容")

            # 提取文本内容
            text_content = content.get_text(separator='\n', strip=True)

            # 过滤掉一些无用的内容
            lines = text_content.split('\n')
            filtered_lines = []

            for line in lines:
                line = line.strip()
                if line and len(line) > 2:
                    # 跳过一些常见的无用内容
                    if not any(skip in line.lower() for skip in [
                        'this page requires javascript',
                        'please turn on javascript',
                        'loading',
                        'skip to content'
                    ]):
                        filtered_lines.append(line)

            final_content = '\n'.join(filtered_lines)

            if final_content and len(final_content.strip()) > 50:
                logging.info(f"Selenium成功提取内容，长度: {len(final_content)} 字符")
                return final_content
            else:
                logging.warning(f"Selenium提取的内容过短: {len(final_content)} 字符")
                return None

        except Exception as e:
            logging.error(f"Selenium提取内容失败 {notes_url}: {e}")
            return None

    def extract_release_notes_content(self, notes_url):
        """提取release notes内容（优先使用Selenium）"""
        # 首先尝试使用Selenium
        if self.use_selenium:
            content = self.extract_release_notes_content_selenium(notes_url)
            if content:
                return content
            else:
                logging.info("Selenium提取失败，尝试使用requests")

        # 回退到requests方式
        try:
            logging.info(f"使用requests提取内容: {notes_url}")
            response = self.session.get(notes_url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 移除不需要的元素
            for element in soup(['script', 'style', 'nav', 'header', 'footer']):
                element.decompose()

            # 尝试找到主要内容区域
            content_selectors = [
                'main',
                '.main-content',
                '.content',
                'article',
                '.release-notes',
                'body'
            ]

            content = None
            for selector in content_selectors:
                content = soup.select_one(selector)
                if content:
                    break

            if not content:
                content = soup

            # 提取文本内容
            text_content = content.get_text(separator='\n', strip=True)

            if text_content and len(text_content.strip()) > 10:
                logging.info(f"requests成功提取内容，长度: {len(text_content)} 字符")
                return text_content
            else:
                logging.warning("requests提取的内容为空或过短")
                return None

        except Exception as e:
            logging.error(f"requests提取release notes内容失败 {notes_url}: {e}")
            return None
    
    def save_release_notes(self, title, content, notes_url):
        """保存release notes到本地文件"""
        # 创建安全的文件名
        safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_title = safe_title.replace(' ', '_')
        filename = f"{safe_title}.txt"
        filepath = os.path.join(self.notes_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"Title: {title}\n")
                f.write(f"URL: {notes_url}\n")
                f.write(f"Extracted: {datetime.now().isoformat()}\n")
                f.write("=" * 80 + "\n\n")
                f.write(content)
            
            logging.info(f"Release notes已保存: {filepath}")
            return filepath
        except Exception as e:
            logging.error(f"保存release notes失败: {e}")
            return None
    
    def process_new_releases(self):
        """处理新的releases"""
        rss_content = self.fetch_rss()
        if not rss_content:
            return
        
        items = self.parse_rss(rss_content)
        new_releases = []
        
        for item in items:
            if item['guid'] not in self.processed_releases:
                new_releases.append(item)
        
        if not new_releases:
            logging.info("没有新的releases")
            return
        
        logging.info(f"发现 {len(new_releases)} 个新releases")
        
        for item in new_releases:
            logging.info(f"处理: {item['title']}")
            
            # 提取release notes链接
            notes_url = self.extract_release_notes_url(item['link'])
            if not notes_url:
                logging.warning(f"未找到release notes链接: {item['title']}")
                continue
            
            # 提取release notes内容
            content = self.extract_release_notes_content(notes_url)
            if not content:
                logging.warning(f"未能提取release notes内容: {item['title']}")
                continue
            
            # 保存到本地
            filepath = self.save_release_notes(item['title'], content, notes_url)
            if filepath:
                self.processed_releases.add(item['guid'])
                logging.info(f"成功处理: {item['title']}")
        
        # 保存处理记录
        self.save_processed_releases()

    def cleanup(self):
        """清理资源"""
        self.close_selenium_driver()

    def debug_single_release(self, release_title_pattern):
        """调试单个release的处理过程"""
        logging.info(f"调试模式：查找包含 '{release_title_pattern}' 的release")

        try:
            rss_content = self.fetch_rss()
            if not rss_content:
                return

            items = self.parse_rss(rss_content)

            for item in items:
                if release_title_pattern.lower() in item['title'].lower():
                    logging.info(f"找到匹配的release: {item['title']}")
                    logging.info(f"Release链接: {item['link']}")
                    logging.info(f"GUID: {item['guid']}")

                    # 检查是否已处理
                    if item['guid'] in self.processed_releases:
                        logging.info("该release已被处理过，但在调试模式下继续处理")

                    # 提取release notes链接
                    notes_url = self.extract_release_notes_url(item['link'])
                    if notes_url:
                        logging.info(f"Release notes URL: {notes_url}")

                        # 提取内容
                        content = self.extract_release_notes_content(notes_url)
                        if content:
                            logging.info(f"成功提取内容，长度: {len(content)} 字符")
                            logging.info(f"内容预览: {content[:200]}...")

                            # 保存内容（调试模式下使用不同的文件名）
                            debug_title = f"DEBUG_{item['title']}"
                            filepath = self.save_release_notes(debug_title, content, notes_url)
                            if filepath:
                                logging.info(f"内容已保存到: {filepath}")
                        else:
                            logging.error("内容提取失败")
                    else:
                        logging.error("未找到release notes链接")
                    return  # 找到第一个匹配的就返回

            logging.warning(f"未找到包含 '{release_title_pattern}' 的release")
        finally:
            self.cleanup()
    
    def run_once(self):
        """运行一次检查"""
        logging.info("开始检查Apple Developer releases...")
        self.process_new_releases()
        logging.info("检查完成")
    
    def run_continuous(self, interval_minutes=30):
        """持续运行监控"""
        logging.info(f"开始持续监控，检查间隔: {interval_minutes} 分钟")

        try:
            while True:
                try:
                    self.run_once()
                    time.sleep(interval_minutes * 60)
                except KeyboardInterrupt:
                    logging.info("监控已停止")
                    break
                except Exception as e:
                    logging.error(f"监控过程中出错: {e}")
                    time.sleep(60)  # 出错后等待1分钟再继续
        finally:
            self.cleanup()

if __name__ == "__main__":
    monitor = AppleReleaseMonitor()

    try:
        # 可以选择运行一次或持续监控
        import sys
        if len(sys.argv) > 1:
            if sys.argv[1] == "--once":
                monitor.run_once()
            elif sys.argv[1] == "--debug" and len(sys.argv) > 2:
                # 调试模式：python apple_release_monitor.py --debug "iOS 26"
                monitor.debug_single_release(sys.argv[2])
            else:
                print("用法:")
                print("  python apple_release_monitor.py                    # 持续监控")
                print("  python apple_release_monitor.py --once             # 运行一次")
                print("  python apple_release_monitor.py --debug 'iOS 26'   # 调试特定release")
        else:
            monitor.run_continuous(interval_minutes=30)
    finally:
        monitor.cleanup()
