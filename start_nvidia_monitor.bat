@echo off
echo NVIDIA驱动监控程序
echo ==================
echo.
echo 选择程序版本:
echo 1. 简洁版 (推荐)
echo 2. 健壮版 (更多错误处理)
echo 3. 退出
echo.
set /p version=请选择版本 (1-3):

if "%version%"=="1" (
    set script=nvidia_driver_monitor_simple.py
) else if "%version%"=="2" (
    set script=nvidia_driver_monitor_robust.py
) else if "%version%"=="3" (
    exit
) else (
    echo 无效选择，请重新运行
    pause
    goto :eof
)

echo.
echo 选择运行模式:
echo 1. 一次性检查
echo 2. 持续监控模式 (每6小时检查一次)
echo 3. 返回
echo.
set /p choice=请输入选择 (1-3):

if "%choice%"=="1" (
    echo.
    echo 开始一次性检查...
    python %script% --once
    pause
) else if "%choice%"=="2" (
    echo.
    echo 开始持续监控模式...
    echo 按 Ctrl+C 停止监控
    python %script%
    pause
) else if "%choice%"=="3" (
    goto :eof
) else (
    echo 无效选择，请重新运行
    pause
)
